<template>
    <el-dialog
            title="直播回放"
            :visible="isShow"
            width="700px"
            :before-close="handleClose">
        <el-form :model="formData" label-width="90px">
            <el-form-item label="回放状态:">
                <el-radio-group v-model="formData.is_playback" @change="handleIsPlayBackChange">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <el-table :data="list" class="mt25">
            <el-table-column label="创建时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <!--            <el-table-column label="视频" width="300" align="center">
                            <template slot-scope="scope">
                                <video :src="scope.row.video_url" controls width="300px" height="200px"></video>
                            </template>
                        </el-table-column>-->
            <el-table-column label="视频格式" prop="file_format" align="center">
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="openPlayDialog(scope.$index)">查看回放</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">关 闭</el-button>
        </div>
        <el-dialog
                title="查看回放"
                :visible="playIsShow"
                width="900px"
                :modal-append-to-body="false"
                :append-to-body="true"
                :before-close="playBackClose">
<!--            <video :src="playUrl" controls width="100%" height="100%"></video>-->
            <div>
                <video v-if="playIsShow" ref="videoPlayer" class="video-js vjs-default-skin vjs-big-play-centered video" preload="auto" controls>
                    <source :src="playUrl" type="application/x-mpegURL"/>
                </video>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="playBackClose">关 闭</el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>
<script>
import {getShareLiveRoomRecordFileByRoomId, SaveShareLiveRoomIsPlayBack} from "@/api/shareLive";
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import 'videojs-contrib-hls'
export default {
    data() {
        return {
            isShow: false,
            list: [],
            playUrl: "",
            playIsShow: false,
            formData: {
                id: null,
                is_playback: 2,
            },
            videoJs: null
        }
    },
    methods: {
        playBackClose(){
            // 安全销毁 VideoJS 实例
            if (this.videoJs && typeof this.videoJs.dispose === 'function') {
                try {
                    this.videoJs.dispose();
                } catch (error) {
                    console.warn('VideoJS dispose error:', error);
                }
            }
            this.videoJs = null;
            this.playIsShow = false;
            this.playUrl = '';
        },
        async handleIsPlayBackChange(val) {
            let params = {
                id: this.formData.id,
                is_playback: this.formData.is_playback
            }
            const {code, msg} = await SaveShareLiveRoomIsPlayBack(params)
            if (code === 0) {
                this.$message.success(msg)
            }
        },
        init(id, is_playback) {
            this.isShow = true
            this.getList(id)
            this.formData.id = id
            this.formData.is_playback = is_playback
        },
        // 获取列表
        async getList(id) {
            const {code, data} = await getShareLiveRoomRecordFileByRoomId({share_live_room_id: id})
            if (code === 0) {
                this.list = data
                console.log(data);
            }
        },
        handleClose() {
            this.isShow = false
            this.list = []
            this.formData = {
                id: null,
                is_playback: 2,
            }
            this.$emit('reLoad')
        },
        openPlayDialog(index) {
            // 先销毁之前的实例（如果存在）
            if (this.videoJs && typeof this.videoJs.dispose === 'function') {
                try {
                    this.videoJs.dispose();
                } catch (error) {
                    console.warn('VideoJS dispose error:', error);
                }
                this.videoJs = null;
            }

            this.playUrl = this.list[index].video_url
            this.playIsShow = true

            // 使用 $nextTick 确保 DOM 完全渲染后再初始化
            this.$nextTick(() => {
                // 再次确认 DOM 元素存在
                if (this.$refs.videoPlayer && this.playIsShow) {
                    try {
                        this.videoJs = videojs(this.$refs.videoPlayer, {
                            "controls": true,
                            "autoplay": false,
                            playbackRates: [0.5, 1, 1.5, 2],
                            fluid: true,
                            responsive: true
                        });
                    } catch (error) {
                        console.error('VideoJS initialization error:', error);
                    }
                }
            });
        }
    },
    // 组件销毁时清理 VideoJS 实例
    beforeDestroy() {
        if (this.videoJs && typeof this.videoJs.dispose === 'function') {
            try {
                this.videoJs.dispose();
            } catch (error) {
                console.warn('VideoJS dispose error in beforeDestroy:', error);
            }
            this.videoJs = null;
        }
    }
}
</script>
<style scoped lang="scss">
.video {
    width: 100%;
    max-height: 500px;
}
</style>