package service

import (
	"finance/model"
	"finance/request"
	"finance/service"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"os"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

func ExportSettlement(info request.AccountApplySearch) (err error, link string) {
	db := source.DB().Model(&model.SupplierSettlement{}).Where("supplier_id = ?", info.SupplierID)

	// 如果有条件搜索 下方会自动创建搜索语句

	var joinWhere string
	if info.Type == 1 && info.Username != "" {

		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id and suppliers.id=" + info.Username
	}

	if info.Type == 2 && info.Username != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id and suppliers.name LIKE " + "'%" + info.Username + "%'"

	}
	if info.Type == 3 && info.Username != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id and suppliers.mobile=" + info.Username

	}
	if info.SettID > 0 {
		db = db.Where("supplier_settlements.id = ?", info.SettID)
	}
	if info.OrderSn > 0 {
		db = db.Where("supplier_settlements.order_sn = ?", info.OrderSn)
	}
	if info.Status > 0 {
		db = db.Where("supplier_settlements.status = ?", info.Status-1)
	}
	if info.PayType != nil {
		db = db.Where("supplier_settlements.pay_type = ?", info.PayType)
	}

	if info.TimeS != "" && info.TimeE != "" {
		db = db.Where("supplier_settlements.settlement_time BETWEEN ? AND ?", info.TimeS, info.TimeE)

	}
	var balanceList []model.SupplierSettlement

	err = db.Joins(joinWhere).Preload(clause.Associations).Order("created_at DESC").Find(&balanceList).Error
	if err != nil {
		return
	}
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "id")
	f.SetCellValue("Sheet1", "B1", "订单编号")
	f.SetCellValue("Sheet1", "C1", "下单时间")
	f.SetCellValue("Sheet1", "D1", "供应商id")
	f.SetCellValue("Sheet1", "E1", "供应商")
	f.SetCellValue("Sheet1", "F1", "支付方式")
	f.SetCellValue("Sheet1", "G1", "支付时间")
	f.SetCellValue("Sheet1", "H1", "订单商品金额")
	f.SetCellValue("Sheet1", "I1", "订单商品供货价")
	f.SetCellValue("Sheet1", "J1", "运费金额")
	f.SetCellValue("Sheet1", "K1", "提现手续费")
	f.SetCellValue("Sheet1", "L1", "技术服务费")
	f.SetCellValue("Sheet1", "M1", "结算金额")
	f.SetCellValue("Sheet1", "N1", "状态")

	i := 2

	for _, v := range balanceList {

		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), strconv.Itoa(int(v.Order.OrderSN)))
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.Order.CreatedAt.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.SupplierID)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.Supplier.Name)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), service.GetPayName(v.PayType))
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.Order.PaidAt.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), service.Fen2Yuan(v.Order.Amount))
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), service.Fen2Yuan(v.Order.SupplyAmount))
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), service.Fen2Yuan(v.Order.Freight))
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), service.Fen2Yuan(v.WithdrawalFee))
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), service.Fen2Yuan(v.TechnicalServiceCost))
		f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), service.Fen2Yuan(v.SettlementAmount))
		f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), service.GetSettlementStatus(v.Status))
		i++

	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	time := time.Now().Format("**************")
	path := config.Config().Local.Path + "/export_settlement"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + time + "结算流水导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}

	return
}

func GetSupplierSettlementData(info request.AccountApplySearch) (err error, list interface{}) {
	var dataMap map[string]int64
	dataMap = make(map[string]int64)

	var totalCount, incomeAmount, spendingAmount, totalAmount int64

	db := source.DB().Model(&model.SupplierSettlement{}).Where("supplier_id = ?", info.SupplierID)

	// 如果有条件搜索 下方会自动创建搜索语句

	var joinWhere string
	if info.Type == 1 && info.Username != "" {

		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id and suppliers.id=" + info.Username
	}

	if info.Type == 2 && info.Username != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id and suppliers.name LIKE " + "'%" + info.Username + "%'"

	}
	if info.Type == 3 && info.Username != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id and suppliers.mobile=" + info.Username

	}
	if info.SettID > 0 {
		db = db.Where("supplier_settlements.id = ?", info.SettID)
	}
	if info.OrderSn > 0 {
		db = db.Where("supplier_settlements.order_sn = ?", info.OrderSn)
	}
	if info.Status > 0 {
		db = db.Where("supplier_settlements.status = ?", info.Status-1)
	}
	if info.PayType != nil {
		db = db.Where("supplier_settlements.pay_type = ?", info.PayType)
	}

	if info.TimeS != "" && info.TimeE != "" {
		db = db.Where("supplier_settlements.settlement_time BETWEEN ? AND ?", info.TimeS, info.TimeE)

	}

	err = db.Joins(joinWhere).Count(&totalCount).Error
	err = db.Select("COALESCE(SUM(settlement_amount), 0) as amount1").First(&incomeAmount).Error
	err = db.Select("COALESCE(SUM(technical_service_cost), 0) as amount1").First(&spendingAmount).Error
	err = db.Select("COALESCE(SUM(withdrawal_fee), 0) as amount1").First(&totalAmount).Error

	//
	//
	//err = source.DB().Model(&model.SupplierSettlement{}).Where("supplier_id = ?",supplierId).Count(&totalCount).Error
	//if err != nil {
	//	return
	//}
	//err = source.DB().Model(&model.SupplierSettlement{}).Where("supplier_id = ?",supplierId).Pluck("COALESCE(SUM(settlement_amount), 0) as amount1", &incomeAmount).Error
	//if err != nil {
	//	return
	//}
	//err = source.DB().Model(&model.SupplierSettlement{}).Where("supplier_id = ?",supplierId).Pluck("COALESCE(SUM(technical_service_cost), 0) as amount1", &spendingAmount).Error
	//if err != nil {
	//	return
	//}
	//err = source.DB().Model(&model.SupplierSettlement{}).Where("supplier_id = ?",supplierId).Pluck("COALESCE(SUM(withdrawal_fee), 0) as amount1", &totalAmount).Error
	//if err != nil {
	//	return
	//}
	dataMap["total_count"] = totalCount
	dataMap["income_amount"] = incomeAmount
	dataMap["spending_amount"] = spendingAmount + totalAmount
	dataMap["total_amount"] = totalAmount
	list = dataMap
	return
}

func GetTotalBill(info request.SupplierBillSearch) (err error, data service.SettlementDataTotal) {

	// 创建db

	var settlementStats service.SettlementDataTotal

	// 查询供应商结算统计数据
	settlementDB := source.DB().Model(&model.SupplierSettlement{})

	orderDB := source.DB().Model(&Order{})

	orderDB.Where("supplier_id  =? ", info.SupplierID)
	settlementDB.Where("supplier_id  =? ", info.SupplierID)

	orderDB.Select("count(*) as order_total_count").Row().Scan(&settlementStats.OrderTotalCount)
	orderDB.Select("COALESCE(SUM(amount), 0)").Row().Scan(&settlementStats.OrderTotalAmount)

	// 订单总金额和订单数量
	settlementDB.Select("COALESCE(SUM(settlement_amount), 0) as order_total_amount").Row().Scan(&settlementStats.OrderTotalAmount, &settlementStats.OrderTotalCount)

	// 账单总金额（结算金额）
	settlementDB.Select("COALESCE(SUM(settlement_amount), 0) as bill_total_amount").Row().Scan(&settlementStats.BillTotalAmount)

	// 技术服务费总金额
	settlementDB.Select("COALESCE(SUM(technical_service_cost), 0) as technical_services_total_amount").Row().Scan(&settlementStats.TechnicalServicesTotalAmount)

	// 已提现金额（已经申请提现的）
	settlementDB.Where("withdrawal_status=2").Select("COALESCE(SUM(settlement_amount), 0) as withdrawn_total_amount").Row().Scan(&settlementStats.WithdrawnTotalAmount)

	// 未提现金额（状态为未提现的）
	//var accountBalance model.AccountBalance

	err = source.DB().Model(&model.AccountBalance{}).Where("uid=?", info.Uid).Where("type=?", 2).Select("settlement_balance").Scan(&settlementStats.NotWithdrawn).Error
	if err != nil {
		log.Log().Error("查询未提现金额失败", zap.Any("err", err))
	}

	//settlementDB.Where("withdrawal_status = 0").Select("COALESCE(SUM(settlement_amount), 0) as not_withdrawn").Row().Scan(&settlementStats.NotWithdrawn)

	// 提现中金额（状态为处理中的）
	settlementDB.Where("withdrawal_status=1 and remit_status=3").Select("COALESCE(SUM(settlement_amount), 0) as withdrawing_in_progress").Row().Scan(&settlementStats.WithdrawingInProgress)

	// 提现手续费总额
	settlementDB.Select("COALESCE(SUM(withdrawal_fee), 0) as withdrawal_total_fee").Row().Scan(&settlementStats.WithdrawalTotalFee)

	// 无效账单金额（状态为无效的）
	settlementDB.Where("withdrawal_status=2").Select("COALESCE(SUM(settlement_amount), 0) as invalid_bill").Row().Scan(&settlementStats.InvalidBill)

	// 已支付金额（所有已完成支付的结算金额）
	settlementDB.Where("(withdrawal_status=1 and remit_status=1").Select("COALESCE(SUM(settlement_amount), 0) as paid_amount").Row().Scan(&settlementStats.PaidAmount)

	var param model.AccountBalance
	source.DB().Where("uid = ? and type =2", info.Uid).First(&param)
	settlementStats.SettlementBalance = param.SettlementBalance
	return err, settlementStats

}

func GetSupplierBill(info request.WithdrawSearch) (err error, withdraw []model.Withdrawal, total int64) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := source.DB().Model(model.Withdrawal{})
	// 供应商名称搜索
	if info.SupplierName != "" {
		var supplierIDs []uint
		err = source.DB().Model(model.Supplier{}).Where("name like ?", "%"+info.SupplierName+"%").Pluck("id", &supplierIDs).Error
		if err != nil {
			db = db.Where("supplier_id = ?", 0)
		}
		db.Where("supplier_id in (?)", supplierIDs)
	}

	// 提现单号
	if info.OrderSn != "" {
		db.Where("order_sn=?", info.OrderSn)
	}
	// 类型
	if info.WithdrawalType > 0 {
		db.Where("withdrawal_type=?", info.WithdrawalType)
	}
	// 提现方式
	if info.WithdrawalMode > 0 {
		db.Where("withdrawal_mode=?", info.WithdrawalMode)
	}
	// 供应商
	if info.SupplierId > 0 {
		db.Where("supplier_id=?", info.SupplierId)
	}
	// 申请时间
	if info.StartTime != "" {
		db = db.Where("created_at >= ?", info.StartTime)
	}
	if info.EndTime != "" {
		db = db.Where("created_at <= ?", info.EndTime)
	}

	if info.RemitStatus != nil {
		db = db.Where("remit_status = ?", info.RemitStatus)
	}

	if info.WithdrawalStatus != nil {
		db.Where("withdrawal_status=?", info.WithdrawalStatus)

	}

	if info.InvoiceStatus != nil {
		db.Where("invoice_status=?", info.InvoiceStatus)

	}

	if info.Status != nil {
		// 待审核
		if *info.Status == 1 {
			db.Where("withdrawal_status=0")
		}
		// 代打款
		if *info.Status == 2 {
			db.Where("withdrawal_status=1 and remit_status=0")
		}
		// 打款中
		if *info.Status == 3 {
			db.Where("withdrawal_status=1 and remit_status=3")
		}
		// 已完成
		if *info.Status == 4 {
			db.Where("withdrawal_status=1 and remit_status=1")
		}
		// 已驳回
		if *info.Status == 5 {
			db.Where("withdrawal_status=3")
		}
		// 已失效
		if *info.Status == 6 {
			db.Where("withdrawal_status=2")
		}

	}

	db.Count(&total)
	err = db.Preload("SingPay").Preload("Supplier").Preload("WithdrawalOperation").Preload("User").Preload("Bank").Limit(limit).Offset(offset).Order("id desc").Find(&withdraw).Error

	return

}

func GetSupplierSettlementList(info request.AccountApplySearch, supplierID uint) (err error, list interface{}, total int64) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.SupplierSettlement{}).Where("supplier_id = ?", supplierID)
	var supplierSettlement []model.SupplierSettlement
	// 如果有条件搜索 下方会自动创建搜索语句

	var joinWhere string
	joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id"
	if info.Type == 1 && info.Username != "" {

		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id and suppliers.id=" + info.Username
	}

	if info.Type == 2 && info.Username != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id and suppliers.name LIKE " + "'%" + info.Username + "%'"

	}
	if info.PayType != nil {
		db = db.Where("supplier_settlements.pay_type = ?", info.PayType)

	}
	if info.Type == 3 && info.Username != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id and suppliers.mobile=" + info.Username

	}
	if info.SettID > 0 {
		db = db.Where("supplier_settlements.id = ?", info.SettID)
	}
	if info.OrderSn > 0 {
		db = db.Where("supplier_settlements.order_sn = ?", info.OrderSn)
	}

	if info.Status > 0 {
		db = db.Where("supplier_settlements.status = ?", info.Status-1)
	}

	if info.TimeS != "" && info.TimeE != "" {
		db = db.Where("supplier_settlements.settlement_time BETWEEN ? AND ?", info.TimeS, info.TimeE)

	}

	err = db.Joins(joinWhere).Count(&total).Error
	err = db.Select("supplier_settlements.*").Limit(limit).Offset(offset).Preload("Order").Preload("Supplier").Order("supplier_settlements.id desc").Find(&supplierSettlement).Error
	return err, supplierSettlement, total

}
