<template>
    <el-dialog
            title="直播回放"
            :visible="isShow"
            width="700px"
            :before-close="handleClose">
        <el-form :model="formData" label-width="90px">
            <el-form-item label="回放状态:">
                <el-radio-group v-model="formData.is_playback" @change="handleIsPlayBackChange">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <el-table :data="list" class="mt25">
            <el-table-column label="创建时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <!--            <el-table-column label="视频" width="300" align="center">
                            <template slot-scope="scope">
                                <video :src="scope.row.video_url" controls width="300px" height="200px"></video>
                            </template>
                        </el-table-column>-->
            <el-table-column label="视频格式" prop="file_format" align="center">
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="openPlayDialog(scope.$index)">查看回放</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">关 闭</el-button>
        </div>
        <el-dialog
                title="查看回放"
                :visible="playIsShow"
                width="900px"
                :modal-append-to-body="false"
                :append-to-body="true"
                :before-close="playBackClose">
<!--            <video :src="playUrl" controls width="100%" height="100%"></video>-->
            <div ref="videoContainer" class="video-container">
                <!-- VideoJS 播放器将动态插入到这里 -->
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="playBackClose">关 闭</el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>
<script>
import {getShareLiveRoomRecordFileByRoomId, SaveShareLiveRoomIsPlayBack} from "@/api/shareLive";
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import 'videojs-contrib-hls'
export default {
    data() {
        return {
            isShow: false,
            list: [],
            playUrl: "",
            playIsShow: false,
            formData: {
                id: null,
                is_playback: 2,
            },
            videoJs: null
        }
    },
    methods: {
        playBackClose(){
            // 先销毁 VideoJS 实例
            this.destroyVideoPlayer();
            // 然后关闭对话框
            this.playIsShow = false;
            this.playUrl = '';
        },
        async handleIsPlayBackChange(val) {
            let params = {
                id: this.formData.id,
                is_playback: this.formData.is_playback
            }
            const {code, msg} = await SaveShareLiveRoomIsPlayBack(params)
            if (code === 0) {
                this.$message.success(msg)
            }
        },
        init(id, is_playback) {
            this.isShow = true
            this.getList(id)
            this.formData.id = id
            this.formData.is_playback = is_playback
        },
        // 获取列表
        async getList(id) {
            const {code, data} = await getShareLiveRoomRecordFileByRoomId({share_live_room_id: id})
            if (code === 0) {
                this.list = data
                console.log(data);
            }
        },
        handleClose() {
            this.isShow = false
            this.list = []
            this.formData = {
                id: null,
                is_playback: 2,
            }
            this.$emit('reLoad')
        },
        openPlayDialog(index) {
            this.playUrl = this.list[index].video_url
            this.playIsShow = true

            // 使用 $nextTick 确保对话框完全打开后再初始化
            this.$nextTick(() => {
                // 等待对话框动画完成
                setTimeout(() => {
                    this.initVideoPlayer();
                }, 300);
            });
        },

        initVideoPlayer() {
            // 先销毁之前的实例（如果存在）
            this.destroyVideoPlayer();

            // 确认容器存在且对话框已打开
            if (this.$refs.videoContainer && this.playIsShow && this.playUrl) {
                try {
                    // 创建新的 video 元素
                    const videoElement = document.createElement('video');
                    videoElement.className = 'video-js vjs-default-skin vjs-big-play-centered video';
                    videoElement.setAttribute('preload', 'auto');
                    videoElement.setAttribute('controls', 'true');
                    videoElement.setAttribute('data-setup', '{}');

                    // 创建 source 元素
                    const sourceElement = document.createElement('source');
                    sourceElement.src = this.playUrl;
                    sourceElement.type = 'application/x-mpegURL';
                    videoElement.appendChild(sourceElement);

                    // 清空容器并添加新的 video 元素
                    this.$refs.videoContainer.innerHTML = '';
                    this.$refs.videoContainer.appendChild(videoElement);

                    // 初始化 VideoJS
                    this.videoJs = videojs(videoElement, {
                        "controls": true,
                        "autoplay": false,
                        playbackRates: [0.5, 1, 1.5, 2],
                        fluid: true,
                        responsive: true,
                        techOrder: ['html5']
                    });

                    // 监听播放器准备就绪事件
                    this.videoJs.ready(() => {
                        console.log('VideoJS player is ready');
                    });

                } catch (error) {
                    console.error('VideoJS initialization error:', error);
                }
            }
        },

        destroyVideoPlayer() {
            if (this.videoJs && typeof this.videoJs.dispose === 'function') {
                try {
                    this.videoJs.dispose();
                } catch (error) {
                    console.warn('VideoJS dispose error:', error);
                }
            }
            this.videoJs = null;
        }
    },
    // 组件销毁时清理 VideoJS 实例
    beforeDestroy() {
        this.destroyVideoPlayer();
    }
}
</script>
<style scoped lang="scss">
.video {
    width: 100%;
    max-height: 500px;
}
</style>