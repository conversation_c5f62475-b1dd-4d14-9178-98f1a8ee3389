package v1

import (
	v1 "gin-vue-admin/admin/api/v1"
	productCommon "product/common"
	"product/model"
	"product/request"
	service2 "product/service"
	"strconv"
	request2 "supplier/request"
	"supplier/response"
	"supplier/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	service3 "yz-go/service"
	"yz-go/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// @Tags Product
// @Summary 用id查询Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Product true "用id查询Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /product/findProduct [get]
func FindProduct(c *gin.Context) {
	var requestProduct model.Product
	var product service2.ProductForUpdate
	var err error
	_ = c.ShouldBindQuery(&requestProduct)
	err, product = service2.GetProductForUpdate(requestProduct.ID)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	if len(product.Skus) <= 1 {
		// 规格数不大于1 使用表中数据
		if len(product.Skus) == 1 {
			product.SkuID = product.Skus[0].ID
			product.Price = product.Skus[0].Price
			product.CostPrice = product.Skus[0].CostPrice
			product.OriginPrice = product.Skus[0].OriginPrice
			product.GuidePrice = product.Skus[0].GuidePrice
			product.ActivityPrice = product.Skus[0].ActivityPrice
			product.Stock = uint(product.Skus[0].Stock)
			product.Sn = product.Skus[0].Sn
		}
	} else {
		// 规格大于一个默认多规格
		product.SingleOption = 0
	}
	yzResponse.OkWithData(gin.H{"reproduct": product}, c)
}

// @Tags Product
// @Summary 分页获取Product列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "分页获取Product列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /product/getProductList [get]
func GetProductList(c *gin.Context) {
	var pageInfo request.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))

	pageInfo.SupplierID = &supplier.ID
	if err, list, total, delTotal, upTotal, downTotal, noTotal := service.GetProductInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(response.ProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			}, SupplierID: *pageInfo.SupplierID, NeedVerify: supplier.NeedVerify, DelTotal: delTotal, UpTotal: upTotal, DownTotal: downTotal, NoTotal: noTotal}, "获取成功", c)
	}
}

func UnscopedProductByIds(c *gin.Context) {
	var IDS request.ProductDetailSearch
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量恢复失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量恢复失败", c)
		return
	}
	if err := utils.Verify(IDS); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err = service.UnscopedProductByIds(IDS, supplier.ID); err != nil {
		log.Log().Error("批量恢复失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("批量恢复成功", c)
	}
}

func CancelProductVerifyByIds(c *gin.Context) {
	var IDS request.ProductDetailSearch
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量取消审核失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量取消审核失败", c)
		return
	}
	if err := utils.Verify(IDS); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err = service.CancelProductVerifyByIds(IDS, supplier.ID); err != nil {
		log.Log().Error("批量取消审核失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("批量取消审核成功", c)
	}
}

func CreateProduct(c *gin.Context) {
	var product service2.ProductForUpdate
	err := c.ShouldBindJSON(&product)
	if err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if product.SingleOption == 1 || len(product.Skus) == 0 {
		sku := service2.Sku{
			Title:         "默认",
			Price:         product.Price,
			CostPrice:     product.CostPrice,
			OriginPrice:   product.OriginPrice,
			GuidePrice:    product.GuidePrice,
			ActivityPrice: product.ActivityPrice,
			Stock:         int(product.Stock),
			Weight:        product.Weight,
			Options:       model.Options{{SpecName: "规格", SpecItemName: "默认"}},
			ImageUrl:      product.SkuImage,
		}
		product.Skus = append(product.Skus, sku)
	} else {
		product.Price = product.Skus[0].Price
		product.CostPrice = product.Skus[0].CostPrice
		product.OriginPrice = product.Skus[0].OriginPrice
		product.GuidePrice = product.Skus[0].GuidePrice
		product.ActivityPrice = product.Skus[0].ActivityPrice
	}

	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err == nil {
		if supplier.NeedVerify == 1 {
			product.IsDisplay = 0
		}
		product.SupplierID = supplier.ID
	} else {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}

	if err, id := service2.CreateProduct(product); err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		if supplier.NeedVerify == 1 {
			err = service2.InsertVerifyRecord(model.ProductVerify{
				ProductVerifyModel: model.ProductVerifyModel{
					ProductID: id, SupplierID: supplier.ID,
				},
			})
			if err != nil {
				log.Log().Error("添加审核记录失败", zap.Any("err", err))
				yzResponse.FailWithMessage("添加审核记录失败:"+err.Error(), c)
				return
			}
		}
		service3.ProductCreateOperationRecord(v1.GetUserID(c), 1, c.ClientIP(), "创建了商品'"+product.Title+"'", productCommon.CreateLogDetailContent(service2.ProductForUpdate{}, product))

		yzResponse.OkWithMessage("创建成功", c)
	}
}

func UpdateProduct(c *gin.Context) {
	var product service2.ProductForUpdate
	err := c.ShouldBindJSON(&product)
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err == nil && supplier.NeedVerify == 1 {
		err = service2.InsertVerifyRecord(model.ProductVerify{
			ProductVerifyModel: model.ProductVerifyModel{
				ProductID: product.ID, SupplierID: supplier.ID,
			},
		})
		if err != nil {
			log.Log().Error("添加审核记录失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		product.IsDisplay = 0
	}
	product.SupplierID = supplier.ID
	if product.SingleOption == 1 || len(product.Skus) == 0 {
		sku := service2.Sku{
			Title:         "默认",
			Price:         product.Price,
			CostPrice:     product.CostPrice,
			OriginPrice:   product.OriginPrice,
			GuidePrice:    product.GuidePrice,
			ActivityPrice: product.ActivityPrice,
			Stock:         int(product.Stock),
			Weight:        product.Weight,
			Options:       model.Options{{SpecName: "规格", SpecItemName: "默认"}},
			ImageUrl:      product.SkuImage,
		}
		sku.ID = product.SkuID
		product.Skus = append(product.Skus, sku)
	} else {
		product.Price = product.Skus[0].Price
		product.CostPrice = product.Skus[0].CostPrice
		product.OriginPrice = product.Skus[0].OriginPrice
		product.GuidePrice = product.Skus[0].GuidePrice
		product.ActivityPrice = product.Skus[0].ActivityPrice
		product.Sn = product.Skus[0].Sn

	}
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, productOld := service2.GetProductForUpdate(product.ID)

	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	if err := service2.UpdateProduct(product); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {

		service3.ProductCreateOperationRecord(v1.GetUserID(c), 1, c.ClientIP(), "编辑了商品'"+product.Title+"'", productCommon.CreateLogDetailContent(productOld, product))

		yzResponse.OkWithMessage("更新成功", c)
	}
}

func SetAttributeStatus(c *gin.Context) {
	var columnStatus request.ColumnStatus
	err := c.ShouldBindJSON(&columnStatus)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(columnStatus); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err == nil && supplier.NeedVerify == 1 && columnStatus.Column == "is_display" && columnStatus.Status == 1 {
		err = service2.InsertVerifyRecord(model.ProductVerify{
			ProductVerifyModel: model.ProductVerifyModel{
				ProductID: uint(columnStatus.ID), SupplierID: supplier.ID,
			},
		})
		if err != nil {
			log.Log().Error("添加审核记录失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		log.Log().Error("待平台审核通过之后自动上架", zap.Any("err", err))
		yzResponse.FailWithMessage("待平台审核通过之后自动上架", c)
		return
	}
	if err, enable := service2.SetAttributeStatus(columnStatus); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		if columnStatus.Column == "is_display" {
			if enable == 1 {
				service3.CreateOperationRecord(v1.GetUserID(c), 1, c.ClientIP(), "上架了商品'"+strconv.Itoa(enable)+"'")
			} else {
				service3.CreateOperationRecord(v1.GetUserID(c), 1, c.ClientIP(), "下架了商品'"+strconv.Itoa(enable)+"'")
			}
		}

		yzResponse.OkWithData(gin.H{"enable": enable}, c)
	}
}

// 前端通过店铺ID获取店铺分类
func GetSupplierProductCategories(c *gin.Context) {
	var supplierQualifications request2.SupplierQualificationsCategories
	_ = c.ShouldBindQuery(&supplierQualifications)

	if err, categorys := service.GetSupplierProductCategories(supplierQualifications); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("店铺暂无分类或者商品都没有上架", c)
		return
	} else {
		yzResponse.OkWithData(categorys, c)
	}
}

// 前端通过店铺ID获取店铺分类（返回全部三级）
func GetSupplierProductCategoriesAll(c *gin.Context) {
	var supplierQualifications request2.SupplierQualificationsCategories
	_ = c.ShouldBindQuery(&supplierQualifications)

	if err, categorys := service.GetSupplierProductCategoriesAll(supplierQualifications); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("店铺暂无分类或者商品都没有上架", c)
		return
	} else {
		yzResponse.OkWithData(categorys, c)
	}
}

// CopyProduct
// @Tags Product
// @Summary 复制商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Product true "复制商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /product/copyProduct [post]
func CopyProduct(c *gin.Context) {
	var copyProduct request.CopyProduct
	err := c.ShouldBindJSON(&copyProduct)
	if err != nil {
		log.Log().Error("失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var isOut = 0 //1时复制的商品变为下架
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err == nil {
		if supplier.NeedVerify == 1 {
			isOut = 1
		}
	} else {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}

	if err, id := service2.CopyProduct(copyProduct, supplier.ID, isOut); err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		if supplier.NeedVerify == 1 {
			err = service2.InsertVerifyRecord(model.ProductVerify{
				ProductVerifyModel: model.ProductVerifyModel{
					ProductID: id, SupplierID: supplier.ID,
				},
			})
			if err != nil {
				log.Log().Error("添加审核记录失败", zap.Any("err", err))
				yzResponse.FailWithMessage("添加审核记录失败:"+err.Error(), c)
				return
			}
		}
		service3.CreateOperationRecord(v1.GetUserID(c), 1, c.ClientIP(), "供应商:"+supplier.Name+"复制了商品'"+strconv.Itoa(copyProduct.Id)+"',新商品id:"+strconv.Itoa(int(id)))

		yzResponse.OkWithMessage("复制成功", c)

	}
}

func ExportProductList(c *gin.Context) {
	var pageInfo request.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	sysUserID := v1.GetUserID(c)
	err, supplier := service.GetSupplierByUserId(sysUserID)
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	pageInfo.SupplierID = &supplier.ID
	pageInfo.SysUserID = &sysUserID
	err = service2.ExportProductInfoList(pageInfo)
	if err != nil {
		log.Log().Error("导出失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithMessage("导出程序后台处理中，预计10-20分钟导出完成，请于导出列表中查看进度", c)
	}
}

func ExportProductRecordList(c *gin.Context) {
	var pageInfo request.ProductExportRecordRequest
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.SysUserID = v1.GetUserID(c)
	err, total, data := service2.GetProductExportRecordList(pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func DeleteProductExportRecord(c *gin.Context) {
	var request model.ProductExportRecord
	err := c.ShouldBindQuery(&request)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service2.DeleteProductExportRecord(request); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}
