package order

import (
	"fmt"
	"testing"
)

func TestAction(t *testing.T) {

	accessToken := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzY29wZSI6WyJhbGwiXSwiZXhwIjoxNzU1NTAzMjE2LCJqdGkiOiJjM2EwZjBlNi04MDRiLTQxNDEtYTJlZi05NjBhN2YxNTJlOTEiLCJ0ZW5hbnQiOiIwMGEwMWExOS04YWRkLTQ3N2YtYTg1Ni01MTUxN2Q1ZDQ4MTQiLCJjbGllbnRfaWQiOiJiZjc3OTk2NmU1Mzc0OTIzODljNTM0YWExYzk3ZDBmYzowMGEwMWExOS04YWRkLTQ3N2YtYTg1Ni01MTUxN2Q1ZDQ4MTQifQ.oC03iVJVT30BbTJwvZ4BJJOCaQpxj2LNJ970wRDjpv0"

	// 5.1 提交订单

	// 5.2 订单支付
	//result, err := Pay(PayParams{
	//	AccessToken: accessToken,
	//	ThirdOrder:  "24120910151408502651",
	//})

	// 5.3 取消未支付订单
	//result, err := Cancel(CancelParams{
	//	AccessToken: accessToken,
	//	ThirdOrder:  "202307191100000000",
	//})

	// 5.4 确认收货
	//err := Confirm(ConfirmParams{
	//	AccessToken: accessToken,
	//	Domain:      "",
	//	OrderSn:     "24121615340768594588",
	//})

	//5.6 查询订单配送信息
	//result, err := Track(TrackParams{
	//	Domain:      "https://zhiwu.admin.fyuanai.com",
	//	AccessToken: accessToken,
	//	OrderSn:     "25061317381250465059",
	//})

	// 5.12 运费查询
	//result, err := Freight(FreightParams{
	//	Domain:      "https://zhiwu.admin.fyuanai.com",
	//	AccessToken: accessToken,
	//	Address:     "广东省广州市海珠区素社街道海印南涌三期7号楼1303",
	//	SkuInfos: []SkuInfo{
	//		{
	//			Quantity: 1,
	//			SkuId:    "e6c1d1d5-203d-4081-8b35-c000a66b10b1",
	//		},
	//	},
	//})

	//result, err := Freight(FreightParams{
	//	Domain:      "https://zhiwu.admin.fyuanai.com",
	//	AccessToken: accessToken,
	//	Address:     "黑龙江省哈尔滨市松北区松北街道均信担保大厦1107室",
	//	SkuInfos: []SkuInfo{
	//		{
	//			Quantity: 1,
	//			SkuId:    "0ea02f2f-160b-41a6-9a96-75b7da8faca4",
	//		},
	//		{
	//			Quantity: 1,
	//			SkuId:    "37652f50-2d2e-4a45-b4ae-3f7233fa42e0",
	//		},
	//	},
	//})

	// 5.13 查询商品区域限制
	result, err := CheckAreaLimit(CheckAreaLimitParams{
		AccessToken: accessToken,
		Domain:      "https://zhiwu.admin.fyuanai.com",
		Address:     "黑龙江省哈尔滨市松北区松北街道均信担保大厦1107室",
		SkuIds: []string{
			"d305c84f-61a1-44c4-a81c-b682754fdbdf",
		},
	})

	fmt.Println(err)
	fmt.Println(result)
}
