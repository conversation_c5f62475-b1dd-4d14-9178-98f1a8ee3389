<template>
    <el-dialog
            title="直播回放"
            :visible="isShow"
            width="700px"
            :before-close="handleClose">
        <el-form :model="formData" label-width="90px">
            <el-form-item label="回放状态:">
                <el-radio-group v-model="formData.is_playback" @change="handleIsPlayBackChange">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <el-table :data="list" class="mt25">
            <el-table-column label="创建时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <!--            <el-table-column label="视频" width="300" align="center">
                            <template slot-scope="scope">
                                <video :src="scope.row.video_url" controls width="300px" height="200px"></video>
                            </template>
                        </el-table-column>-->
            <el-table-column label="视频格式" prop="file_format" align="center">
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="openPlayDialog(scope.$index)">查看回放</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">关 闭</el-button>
        </div>
        <el-dialog
                title="查看回放"
                :visible="playIsShow"
                width="900px"
                :modal-append-to-body="false"
                :append-to-body="true"
                :before-close="playBackClose">
<!--            <video :src="playUrl" controls width="100%" height="100%"></video>-->
            <div ref="videoContainer" class="video-container">
                <!-- VideoJS 播放器将动态插入到这里 -->
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="playBackClose">关 闭</el-button>
            </div>
        </el-dialog>
    </el-dialog>
</template>
<script>
import {getShareLiveRoomRecordFileByRoomId, SaveShareLiveRoomIsPlayBack} from "@/api/shareLive";
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import 'videojs-contrib-hls'
export default {
    data() {
        return {
            isShow: false,
            list: [],
            playUrl: "",
            playIsShow: false,
            formData: {
                id: null,
                is_playback: 2,
            },
            videoJs: null
        }
    },
    methods: {
        playBackClose(){
            // 先销毁 VideoJS 实例
            this.destroyVideoPlayer();
            // 然后关闭对话框
            this.playIsShow = false;
            this.playUrl = '';
        },
        async handleIsPlayBackChange(val) {
            let params = {
                id: this.formData.id,
                is_playback: this.formData.is_playback
            }
            const {code, msg} = await SaveShareLiveRoomIsPlayBack(params)
            if (code === 0) {
                this.$message.success(msg)
            }
        },
        init(id, is_playback) {
            this.isShow = true
            this.getList(id)
            this.formData.id = id
            this.formData.is_playback = is_playback
        },
        // 获取列表
        async getList(id) {
            const {code, data} = await getShareLiveRoomRecordFileByRoomId({share_live_room_id: id})
            if (code === 0) {
                this.list = data
                console.log(data);
            }
        },
        handleClose() {
            this.isShow = false
            this.list = []
            this.formData = {
                id: null,
                is_playback: 2,
            }
            this.$emit('reLoad')
        },
        openPlayDialog(index) {
            this.playUrl = this.list[index].video_url
            this.playIsShow = true

            // 使用 $nextTick 确保对话框完全打开后再初始化
            this.$nextTick(() => {
                // 等待对话框动画完成
                setTimeout(() => {
                    this.initVideoPlayer();
                }, 300);
            });
        },

        initVideoPlayer() {
            // 先销毁之前的实例（如果存在）
            this.destroyVideoPlayer();

            // 确认容器存在且对话框已打开
            if (this.$refs.videoContainer && this.playIsShow && this.playUrl) {
                try {
                    // 创建新的 video 元素
                    const videoElement = document.createElement('video');
                    videoElement.className = 'video-js vjs-default-skin vjs-big-play-centered video';
                    videoElement.setAttribute('preload', 'metadata');
                    videoElement.setAttribute('controls', '');
                    videoElement.setAttribute('data-setup', '{}');
                    videoElement.setAttribute('playsinline', '');
                    videoElement.setAttribute('webkit-playsinline', '');
                    // 设置最大高度
                    videoElement.style.maxHeight = '500px';
                    videoElement.style.height = 'auto';
                    videoElement.style.width = '100%';

                    // 根据文件扩展名和 URL 内容判断视频类型
                    const getVideoType = (url) => {
                        const extension = url.split('.').pop().toLowerCase();
                        switch (extension) {
                            case 'mp4':
                                return 'video/mp4';
                            case 'webm':
                                return 'video/webm';
                            case 'ogg':
                                return 'video/ogg';
                            case 'm3u8':
                                return 'application/x-mpegURL';
                            case 'flv':
                                return 'video/x-flv';
                            default:
                                // 如果无法从扩展名判断，尝试根据 URL 内容判断
                                if (url.includes('.m3u8') || url.includes('hls') || url.includes('playlist')) {
                                    return 'application/x-mpegURL';
                                }
                                return 'video/mp4'; // 默认为 mp4
                        }
                    };

                    const videoType = getVideoType(this.playUrl);

                    // 创建 source 元素
                    const sourceElement = document.createElement('source');
                    sourceElement.src = this.playUrl;
                    sourceElement.type = videoType;
                    videoElement.appendChild(sourceElement);

                    // 清空容器并添加新的 video 元素
                    this.$refs.videoContainer.innerHTML = '';
                    this.$refs.videoContainer.appendChild(videoElement);

                    // 根据视频类型配置 VideoJS
                    const videoJsConfig = {
                        controls: true,
                        autoplay: false,
                        preload: 'metadata',
                        playbackRates: [0.5, 1, 1.5, 2],
                        fluid: true,
                        responsive: true,
                        techOrder: ['html5'],
                        height: 'auto',
                        maxHeight: 500,
                        // 控制条相关配置
                        inactivityTimeout: 0, // 禁用自动隐藏
                        userActions: {
                            hotkeys: true
                        },
                        // 明确指定要显示的控制组件
                        children: [
                            'mediaLoader',
                            'posterImage',
                            'textTrackDisplay',
                            'loadingSpinner',
                            'bigPlayButton',
                            'liveTracker',
                            'controlBar',
                            'errorDisplay',
                            'textTrackSettings',
                            'resizeManager'
                        ]
                    };

                    // 如果是 HLS 格式，添加 HLS 相关配置
                    if (videoType === 'application/x-mpegURL') {
                        videoJsConfig.html5 = {
                            hls: {
                                enableLowInitialPlaylist: true,
                                smoothQualityChange: true,
                                overrideNative: true
                            }
                        };
                    }

                    // 初始化 VideoJS
                    this.videoJs = videojs(videoElement, videoJsConfig);

                    // 监听播放器准备就绪事件
                    this.videoJs.ready(() => {
                        console.log('VideoJS player is ready');
                        this.forceShowControls();
                    });

                    // 监听视频加载完成事件
                    this.videoJs.on('loadeddata', () => {
                        console.log('Video data loaded');
                        this.forceShowControls();
                    });

                    // 监听视频可以播放事件
                    this.videoJs.on('canplay', () => {
                        console.log('Video can play');
                        this.forceShowControls();
                    });

                    // 监听用户活动，防止控制条自动隐藏
                    this.videoJs.on('userinactive', () => {
                        this.videoJs.userActive(true);
                    });

                    // 监听全屏事件，动态调整样式
                    this.videoJs.on('fullscreenchange', () => {
                        const player = this.videoJs.el();
                        const tech = player.querySelector('.vjs-tech');

                        if (this.videoJs.isFullscreen()) {
                            // 进入全屏时移除高度限制
                            player.style.setProperty('max-height', 'none', 'important');
                            player.style.setProperty('height', '100vh', 'important');
                            if (tech) {
                                tech.style.setProperty('max-height', 'none', 'important');
                                tech.style.setProperty('height', '100vh', 'important');
                                tech.style.setProperty('width', '100vw', 'important');
                            }
                            console.log('进入全屏模式');
                        } else {
                            // 退出全屏时恢复高度限制
                            player.style.setProperty('max-height', '500px', 'important');
                            player.style.setProperty('height', 'auto', 'important');
                            if (tech) {
                                tech.style.setProperty('max-height', '500px', 'important');
                                tech.style.setProperty('height', 'auto', 'important');
                                tech.style.setProperty('width', '100%', 'important');
                            }
                            console.log('退出全屏模式');
                        }
                    });

                } catch (error) {
                    console.error('VideoJS initialization error:', error);
                }
            }
        },

        forceShowControls() {
            if (!this.videoJs) return;

            // 强制显示控制条
            const controlBar = this.videoJs.controlBar;
            if (controlBar) {
                controlBar.show();
                const controlBarEl = controlBar.el();
                if (controlBarEl) {
                    controlBarEl.style.setProperty('display', 'flex', 'important');
                    controlBarEl.style.setProperty('visibility', 'visible', 'important');
                    controlBarEl.style.setProperty('opacity', '1', 'important');
                    controlBarEl.style.setProperty('transform', 'translateY(0)', 'important');
                }
            }

            // 确保播放按钮显示
            const bigPlayButton = this.videoJs.bigPlayButton;
            if (bigPlayButton) {
                bigPlayButton.show();
            }

            // 保持用户活跃状态，防止控制条自动隐藏
            this.videoJs.userActive(true);

            console.log('强制显示控制条完成');
        },

        destroyVideoPlayer() {
            if (this.videoJs && typeof this.videoJs.dispose === 'function') {
                try {
                    this.videoJs.dispose();
                } catch (error) {
                    console.warn('VideoJS dispose error:', error);
                }
            }
            this.videoJs = null;

            // 清空容器
            if (this.$refs.videoContainer) {
                this.$refs.videoContainer.innerHTML = '';
            }
        }
    },
    // 组件销毁时清理 VideoJS 实例
    beforeDestroy() {
        this.destroyVideoPlayer();
    }
}
</script>
<style scoped lang="scss">
.video-container {
    width: 100%;
    min-height: 300px;
    max-height: 500px;
    overflow: hidden;
}

.video {
    width: 100%;
    max-height: 500px;
}

// VideoJS 播放器基础样式
:deep(.video-js) {
    max-height: 500px !important;
    height: auto !important;

    // 确保控制条始终显示
    .vjs-control-bar {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        transform: translateY(0) !important;
        transition: none !important;
    }

    // 禁用用户非活跃状态下的控制条隐藏
    &.vjs-user-inactive .vjs-control-bar {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        transform: translateY(0) !important;
    }

    // 确保播放按钮显示
    .vjs-big-play-button {
        display: block !important;
    }

    // 确保进度条显示
    .vjs-progress-control {
        display: flex !important;
        visibility: visible !important;
    }

    // 确保所有控制按钮显示
    .vjs-play-control,
    .vjs-volume-panel,
    .vjs-current-time,
    .vjs-duration,
    .vjs-playback-rate,
    .vjs-fullscreen-control {
        display: block !important;
        visibility: visible !important;
    }
}

:deep(.vjs-tech) {
    max-height: 500px !important;
    height: auto !important;
}
</style>